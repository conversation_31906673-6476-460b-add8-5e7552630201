# Minimal MCP Server and Client

This project demonstrates how to create a Model Context Protocol (MCP) server and client using the official C# SDK. The implementation includes practical examples of tools, prompts, and client-server communication.

## What is MCP?

The Model Context Protocol (MCP) is an open protocol that standardizes how applications provide context to Large Language Models (LLMs). It enables secure integration between LLMs and various data sources and tools.

- **Official Documentation**: https://modelcontextprotocol.io/
- **Protocol Specification**: https://spec.modelcontextprotocol.io/
- **C# SDK Repository**: https://github.com/modelcontextprotocol/csharp-sdk

## Project Structure

```
MinimalMcpServer/
├── MinimalMcpServer.sln          # Solution file
├── README.md                     # This file
├── src/
│   ├── McpServer/               # MCP Server implementation
│   │   ├── McpServer.csproj
│   │   └── Program.cs
│   └── McpClient/               # MCP Client implementation
│       ├── McpClient.csproj
│       └── Program.cs
```

## Prerequisites

- .NET 8.0 SDK or later
- Visual Studio 2022, VS Code, or any .NET-compatible IDE

## Important Notes

- This implementation uses **ModelContextProtocol v0.2.0-preview.3**
- The C# SDK is currently in preview and APIs may change
- The client implementation is simplified for demonstration purposes
- For production use, refer to the latest SDK documentation

## Installation and Setup

1. **Clone or download this project**
   ```bash
   git clone <repository-url>
   cd MinimalMcpServer
   ```

2. **Restore NuGet packages**
   ```bash
   dotnet restore
   ```

3. **Build the solution**
   ```bash
   dotnet build
   ```

## Running the Examples

### Option 1: Use the Demo Script (Recommended)

Run the provided demo script that builds and runs everything:

```bash
# On macOS/Linux:
./run-demo.sh

# On Windows:
run-demo.bat
```

### Option 2: Run Client Demo

The client demo shows the server capabilities:

```bash
cd src/McpClient
dotnet run
```

This will display information about the available tools and prompts.

### Option 3: Run Server Separately

**Terminal 1 - Start the Server:**
```bash
cd src/McpServer
dotnet run
```

The server will start and wait for connections via stdio transport. You can then connect to it using any MCP-compatible client.

## Features Demonstrated

### 🔧 Tools Available

The server implements several example tools:

1. **Echo** - Echoes a message back to the client
2. **Add** - Adds two numbers together
3. **GetCurrentTime** - Returns the current date and time
4. **GenerateRandomNumber** - Generates a random number within a range
5. **ReadFile** - Reads the contents of a text file
6. **WriteFile** - Writes content to a text file
7. **ListFiles** - Lists files and directories in a specified path

### 📝 Prompts Available

The server provides example prompts for common LLM tasks:

1. **SummarizeContent** - Creates a prompt to summarize provided content
2. **ExplainConcept** - Creates a prompt to explain concepts at different levels
3. **ReviewCode** - Creates a prompt for code review with specific criteria

### 🔌 Transport

- Uses **stdio transport** for communication between client and server
- Server logs to stderr to keep stdout clear for MCP protocol messages
- Client can start server as subprocess or connect to existing server

## Code Examples

### Server Tool Implementation

```csharp
[McpServerToolType]
public static class ExampleTools
{
    [McpServerTool, Description("Echoes the message back to the client.")]
    public static string Echo(
        [Description("The message to echo back")] string message)
    {
        return $"Echo: {message}";
    }

    [McpServerTool, Description("Adds two numbers together.")]
    public static double Add(
        [Description("The first number")] double a,
        [Description("The second number")] double b)
    {
        return a + b;
    }
}
```

### Client Implementation Note

The current client implementation (v0.2.0-preview.3) is a demonstration client that shows the server capabilities. A full client implementation would use the MCP protocol to connect via stdio transport and make JSON-RPC calls.

For the latest client API examples, refer to the [official C# SDK documentation](https://modelcontextprotocol.github.io/csharp-sdk/api/ModelContextProtocol.html).

### Server Prompt Implementation

```csharp
[McpServerPromptType]
public static class ExamplePrompts
{
    [McpServerPrompt, Description("Creates a prompt to summarize content.")]
    public static string SummarizeContent(
        [Description("The content to summarize")] string content)
    {
        return $"Please provide a concise summary of the following content:\n\n{content}";
    }
}
```

## Expected Output

When you run the demo, you should see output similar to:

```
=== MCP Server and Client Demo ===
This script will build and run the MCP client demo.

✅ .NET SDK found: 8.0.411
📦 Restoring NuGet packages...
🔨 Building solution...
✅ Build successful!

🚀 Starting MCP Client Demo...

=== MCP Client Demo ===
This is a simplified client implementation for the MCP C# SDK v0.2.0-preview.3

📋 Server Information:
======================
The MCP server can be started separately using:
  cd src/McpServer
  dotnet run

🔧 Available Tools:
===================
The server implements the following tools:
• Echo - Echoes a message back to the client
• Add - Adds two numbers together
• GetCurrentTime - Gets the current date and time
• GenerateRandomNumber - Generates a random number within a range
• ReadFile - Reads the contents of a text file
• WriteFile - Writes content to a text file
• ListFiles - Lists files and directories in a specified path

🎉 Demo completed successfully!
```

## Troubleshooting

### Common Issues

1. **"Package not found" errors**
   - Ensure you're using the `--prerelease` flag when installing the MCP package
   - The C# SDK is currently in preview

2. **Connection timeouts**
   - Make sure the server project builds successfully
   - Check that .NET 8.0 is installed

3. **Permission errors with file operations**
   - The file tools operate in the current directory
   - Ensure the application has read/write permissions

### Debug Mode

To see detailed logging, modify the server's logging configuration:

```csharp
builder.Logging.SetMinimumLevel(LogLevel.Debug);
```

## Next Steps

- Explore the [official C# SDK documentation](https://modelcontextprotocol.github.io/csharp-sdk/api/ModelContextProtocol.html)
- Try implementing your own tools and prompts
- Experiment with different transport mechanisms (HTTP, WebSocket)
- Integrate with LLM libraries like Semantic Kernel or LangChain

## License

This project is provided as an educational example. See the [MCP C# SDK license](https://github.com/modelcontextprotocol/csharp-sdk/blob/main/LICENSE) for the underlying SDK licensing.
