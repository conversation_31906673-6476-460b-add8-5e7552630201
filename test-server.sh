#!/bin/bash

echo "Testing MCP Server with simple JSON-RPC calls..."
echo ""

# Start the server in the background
cd src/McpServer
dotnet run &
SERVER_PID=$!

# Give the server time to start
sleep 2

echo "Server started with PID: $SERVER_PID"
echo "You can test the server manually by sending JSON-RPC messages to its stdin"
echo "Example initialize request:"
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test-client", "version": "1.0.0"}}}'
echo ""
echo "Press Ctrl+C to stop the server"

# Wait for user to stop
wait $SERVER_PID
