@echo off
echo === MCP Server and Client Demo ===
echo This script will build and run the MCP client demo.
echo.

REM Check if .NET is installed
dotnet --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ .NET SDK is not installed. Please install .NET 8.0 or later.
    echo    Download from: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ .NET SDK found
dotnet --version
echo.

REM Restore packages
echo 📦 Restoring NuGet packages...
dotnet restore
if %errorlevel% neq 0 (
    echo ❌ Failed to restore packages
    pause
    exit /b 1
)

REM Build solution
echo 🔨 Building solution...
dotnet build
if %errorlevel% neq 0 (
    echo ❌ Build failed
    pause
    exit /b 1
)

echo ✅ Build successful!
echo.

REM Run the client demo
echo 🚀 Starting MCP Client Demo...
echo    (The client will automatically start the server)
echo.

cd src\McpClient
dotnet run

echo.
echo 🎉 Demo completed!
pause
