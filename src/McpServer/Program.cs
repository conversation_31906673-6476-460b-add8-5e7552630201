using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ModelContextProtocol.Server;
using System.ComponentModel;

var builder = Host.CreateApplicationBuilder(args);

// Configure logging to stderr so stdout is available for MCP communication
builder.Logging.AddConsole(consoleLogOptions =>
{
    consoleLogOptions.LogToStandardErrorThreshold = LogLevel.Trace;
});

// Add MCP server services
builder.Services
    .AddMcpServer()
    .WithStdioServerTransport()
    .WithToolsFromAssembly()
    .WithPromptsFromAssembly();

// Add HTTP client for tools that need it
builder.Services.AddHttpClient();

// Build and run the host
await builder.Build().RunAsync();

/// <summary>
/// Example tools for the MCP server
/// </summary>
[McpServerToolType]
public static class ExampleTools
{
    [McpServerTool, Description("Echoes the message back to the client.")]
    public static string Echo(
        [Description("The message to echo back")] string message)
    {
        return $"Echo: {message}";
    }

    [McpServerTool, Description("Adds two numbers together.")]
    public static double Add(
        [Description("The first number")] double a,
        [Description("The second number")] double b)
    {
        return a + b;
    }

    [McpServerTool, Description("Gets the current date and time.")]
    public static string GetCurrentTime()
    {
        return DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
    }

    [McpServerTool, Description("Generates a random number between min and max.")]
    public static int GenerateRandomNumber(
        [Description("Minimum value (inclusive)")] int min = 1,
        [Description("Maximum value (inclusive)")] int max = 100)
    {
        var random = new Random();
        return random.Next(min, max + 1);
    }

    [McpServerTool, Description("Reads the contents of a text file.")]
    public static async Task<string> ReadFile(
        [Description("Path to the file to read")] string filePath)
    {
        try
        {
            if (!File.Exists(filePath))
            {
                return $"Error: File '{filePath}' does not exist.";
            }

            var content = await File.ReadAllTextAsync(filePath);
            return $"File content:\n{content}";
        }
        catch (Exception ex)
        {
            return $"Error reading file: {ex.Message}";
        }
    }

    [McpServerTool, Description("Writes text content to a file.")]
    public static async Task<string> WriteFile(
        [Description("Path to the file to write")] string filePath,
        [Description("Content to write to the file")] string content)
    {
        try
        {
            await File.WriteAllTextAsync(filePath, content);
            return $"Successfully wrote content to '{filePath}'";
        }
        catch (Exception ex)
        {
            return $"Error writing file: {ex.Message}";
        }
    }

    [McpServerTool, Description("Lists files in a directory.")]
    public static string ListFiles(
        [Description("Directory path to list files from")] string directoryPath = ".")
    {
        try
        {
            if (!Directory.Exists(directoryPath))
            {
                return $"Error: Directory '{directoryPath}' does not exist.";
            }

            var files = Directory.GetFiles(directoryPath);
            var directories = Directory.GetDirectories(directoryPath);

            var result = $"Contents of '{directoryPath}':\n\nDirectories:\n";
            foreach (var dir in directories)
            {
                result += $"  📁 {Path.GetFileName(dir)}\n";
            }

            result += "\nFiles:\n";
            foreach (var file in files)
            {
                var fileInfo = new FileInfo(file);
                result += $"  📄 {Path.GetFileName(file)} ({fileInfo.Length} bytes)\n";
            }

            return result;
        }
        catch (Exception ex)
        {
            return $"Error listing directory: {ex.Message}";
        }
    }
}

/// <summary>
/// Example prompts for the MCP server
/// </summary>
[McpServerPromptType]
public static class ExamplePrompts
{
    [McpServerPrompt, Description("Creates a prompt to summarize the provided content.")]
    public static string SummarizeContent(
        [Description("The content to summarize")] string content)
    {
        return $"Please provide a concise summary of the following content:\n\n{content}";
    }

    [McpServerPrompt, Description("Creates a prompt to explain a concept in simple terms.")]
    public static string ExplainConcept(
        [Description("The concept to explain")] string concept,
        [Description("Target audience level (beginner, intermediate, advanced)")] string level = "beginner")
    {
        return $"Please explain the concept of '{concept}' in simple terms suitable for a {level} audience. " +
               "Use clear examples and avoid jargon where possible.";
    }

    [McpServerPrompt, Description("Creates a prompt for code review.")]
    public static string ReviewCode(
        [Description("The code to review")] string code,
        [Description("Programming language")] string language)
    {
        return $"Please review the following {language} code and provide feedback on:\n" +
               "1. Code quality and best practices\n" +
               "2. Potential bugs or issues\n" +
               "3. Suggestions for improvement\n\n" +
               $"Code:\n```{language}\n{code}\n```";
    }
}
