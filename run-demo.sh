#!/bin/bash

echo "=== MCP Server and Client Demo ==="
echo "This script will build and run the MCP client demo."
echo ""

# Check if .NET is installed
if ! command -v dotnet &> /dev/null; then
    echo "❌ .NET SDK is not installed. Please install .NET 8.0 or later."
    echo "   Download from: https://dotnet.microsoft.com/download"
    exit 1
fi

echo "✅ .NET SDK found: $(dotnet --version)"
echo ""

# Restore packages
echo "📦 Restoring NuGet packages..."
dotnet restore
if [ $? -ne 0 ]; then
    echo "❌ Failed to restore packages"
    exit 1
fi

# Build solution
echo "🔨 Building solution..."
dotnet build
if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build successful!"
echo ""

# Run the client demo
echo "🚀 Starting MCP Client Demo..."
echo "   (The client will automatically start the server)"
echo ""

cd src/McpClient
dotnet run

echo ""
echo "🎉 Demo completed!"
